# Frontend Credit Card Integration Guide

## Overview
I've updated your `UpgradePlanModal.vue` component to support direct credit card payments using the new backend endpoints. Here's what was added:

## ✨ New Features Added

### 1. Payment Method Selection
- **Snap Payment**: Original popup method (for multiple payment options)
- **Direct Credit Card**: New secure credit card payment (recommended for credit cards)
- **Invoice Payment**: Existing invoice method

### 2. Credit Card Form
- Card number input with automatic formatting
- Expiry date (MM/YY) with validation
- CVV field
- Card holder name
- Real-time form validation

### 3. Secure Payment Flow
- Uses the new `/payments/charge-credit-card-direct` endpoint
- Includes proper `secure: true` parameter
- Handles payment responses appropriately

## 🔧 Implementation Details

### Payment Method Selection
Users can now choose between three payment methods:
1. **Snap Payment** - Opens Midtrans popup with multiple options
2. **Direct Credit Card** - Secure form for credit card details
3. **Invoice Payment** - Generate invoice for later payment

### Credit Card Processing
When users select "Direct Credit Card":
1. A secure form appears for card details
2. Card number is automatically formatted (1234 5678 9012 3456)
3. Expiry date is formatted as MM/YY
4. Form validates all required fields
5. Card is tokenized (currently using mock - see below)
6. Token is sent to your new backend endpoint
7. Payment is processed with `secure: true` parameter

## ⚠️ Important: Tokenization Implementation

The current implementation uses a **mock token** for demonstration. In production, you need to implement proper Midtrans tokenization:

### Option 1: Use Midtrans Snap.js (Recommended)
```javascript
// Add Midtrans Snap.js to your HTML
<script src="https://app.sandbox.midtrans.com/snap/snap.js" data-client-key="YOUR_CLIENT_KEY"></script>

// Replace the getCreditCardToken function with:
const getCreditCardToken = async () => {
  return new Promise((resolve, reject) => {
    const cardData = {
      card_number: creditCard.value.number.replace(/\s/g, ''),
      card_exp_month: creditCard.value.expiry.split('/')[0],
      card_exp_year: '20' + creditCard.value.expiry.split('/')[1],
      card_cvv: creditCard.value.cvv,
      secure: true
    };

    window.snap.pay('dummy-token', {
      skipOrderSummary: true,
      onSuccess: function(result) {
        resolve(result.token);
      },
      onError: function(result) {
        reject(result);
      }
    });
  });
};
```

### Option 2: Direct API Call to Midtrans
```javascript
const getCreditCardToken = async () => {
  const response = await axios.post('https://api.sandbox.midtrans.com/v2/token', {
    card_number: creditCard.value.number.replace(/\s/g, ''),
    card_exp_month: creditCard.value.expiry.split('/')[0],
    card_exp_year: '20' + creditCard.value.expiry.split('/')[1],
    card_cvv: creditCard.value.cvv,
    client_key: 'YOUR_CLIENT_KEY'
  });
  
  return response.data.token_id;
};
```

## 🚀 How to Test

### 1. Test with Mock Token (Current Implementation)
1. Select a plan (Enterprise or Top-up)
2. Choose "Direct Credit Card" payment method
3. Fill in the credit card form with any valid-looking data:
   - Card Number: 4811 1111 1111 1114
   - Expiry: 12/25
   - CVV: 123
   - Name: Test User
4. Click upgrade - it will use a mock token

### 2. Test with Real Midtrans (After Implementing Tokenization)
1. Replace the `getCreditCardToken` function with proper Midtrans implementation
2. Use Midtrans test card numbers:
   - Success: 4811 1111 1111 1114
   - Failure: 4911 1111 1111 1113
3. Test the complete flow

## 📝 Backend Integration

The frontend now calls these new endpoints:
- `POST /payments/charge-credit-card-direct` - For direct credit card charges
- `POST /payments/charge-credit-card` - Alternative method using midtrans-client

Both endpoints expect:
```json
{
  "planId": "enterprise",
  "cardToken": "token-from-midtrans",
  "duration": 1
}
```

## 🔒 Security Notes

1. **Never store credit card details** - Always tokenize immediately
2. **Use HTTPS** - Ensure all communication is encrypted
3. **Validate on backend** - Always verify payments server-side
4. **PCI Compliance** - Follow PCI DSS guidelines for card data handling

## 🎯 Next Steps

1. **Implement proper tokenization** using Midtrans Snap.js or API
2. **Test with Midtrans sandbox** using test card numbers
3. **Add error handling** for failed tokenization
4. **Implement loading states** during tokenization
5. **Add card type detection** (Visa, MasterCard, etc.)
6. **Deploy to production** with production Midtrans keys

The updated component provides a much better user experience for credit card payments and should resolve the `secure: true` parameter issue you were experiencing.
